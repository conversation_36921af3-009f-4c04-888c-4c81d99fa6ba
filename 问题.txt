phy@phy-HP-431-Notebook-PC:/var/www/poetize-master/poetize-server/poetry-web$ java -jar target/poetize-server.jar
Logging system failed to initialize using configuration from 'null'
java.lang.IllegalStateException: Logback configuration error detected: 
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[fileInfoLog] - Failed to create parent directories for [/home/<USER>/logs/poetize.log]
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[fileInfoLog] - openFile(/home/<USER>/logs/poetize.log,true) call failed. java.io.FileNotFoundException: /home/<USER>/logs/poetize.log (没有那个文件或目录)
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[fileErrorLog] - Failed to create parent directories for [/home/<USER>/logs/poetize-error.log]
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[fileErrorLog] - openFile(/home/<USER>/logs/poetize-error.log,true) call failed. java.io.FileNotFoundException: /home/<USER>/logs/poetize-error.log (没有那个文件或目录)
        at org.springframework.boot.logging.logback.LogbackLoggingSystem.reportConfigurationErrorsIfNecessary(LogbackLoggingSystem.java:189)
        at org.springframework.boot.logging.logback.LogbackLoggingSystem.loadConfiguration(LogbackLoggingSystem.java:170)
        at org.springframework.boot.logging.AbstractLoggingSystem.initializeWithConventions(AbstractLoggingSystem.java:80)
        at org.springframework.boot.logging.AbstractLoggingSystem.initialize(AbstractLoggingSystem.java:60)
        at org.springframework.boot.logging.logback.LogbackLoggingSystem.initialize(LogbackLoggingSystem.java:132)
        at org.springframework.boot.context.logging.LoggingApplicationListener.initializeSystem(LoggingApplicationListener.java:329)
        at org.springframework.boot.context.logging.LoggingApplicationListener.initialize(LoggingApplicationListener.java:298)
        at org.springframework.boot.context.logging.LoggingApplicationListener.onApplicationEnvironmentPreparedEvent(LoggingApplicationListener.java:246)
        at org.springframework.boot.context.logging.LoggingApplicationListener.onApplicationEvent(LoggingApplicationListener.java:223)
        at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178)
        at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171)
        at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145)
        at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:133)
        at org.springframework.boot.context.event.EventPublishingRunListener.environmentPrepared(EventPublishingRunListener.java:85)
        at org.springframework.boot.SpringApplicationRunListeners.lambda$environmentPrepared$2(SpringApplicationRunListeners.java:66)
        at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
        at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120)
        at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:114)
        at org.springframework.boot.SpringApplicationRunListeners.environmentPrepared(SpringApplicationRunListeners.java:65)
        at org.springframework.boot.SpringApplication.prepareEnvironment(SpringApplication.java:344)
        at org.springframework.boot.SpringApplication.run(SpringApplication.java:302)
        at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
        at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
        at com.ld.poetry.PoetryApplication.main(PoetryApplication.java:14)
        at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
        at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
        at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
        at java.base/java.lang.reflect.Method.invoke(Method.java:569)
        at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
        at org.springframework.boot.loader.Launcher.launch(Launcher.java:108)
        at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
        at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65)
        Suppressed: java.io.FileNotFoundException: /home/<USER>/logs/poetize.log (没有那个文件或目录)
                at java.base/java.io.FileOutputStream.open0(Native Method)
                at java.base/java.io.FileOutputStream.open(FileOutputStream.java:293)
                at java.base/java.io.FileOutputStream.<init>(FileOutputStream.java:235)
                at ch.qos.logback.core.recovery.ResilientFileOutputStream.<init>(ResilientFileOutputStream.java:26)
                at ch.qos.logback.core.FileAppender.openFile(FileAppender.java:204)
                at ch.qos.logback.core.FileAppender.start(FileAppender.java:127)
                at ch.qos.logback.core.rolling.RollingFileAppender.start(RollingFileAppender.java:100)
                at ch.qos.logback.core.joran.action.AppenderAction.end(AppenderAction.java:90)
                at ch.qos.logback.core.joran.spi.Interpreter.callEndAction(Interpreter.java:309)
                at ch.qos.logback.core.joran.spi.Interpreter.endElement(Interpreter.java:193)
                at ch.qos.logback.core.joran.spi.Interpreter.endElement(Interpreter.java:179)
                at ch.qos.logback.core.joran.spi.EventPlayer.play(EventPlayer.java:62)
                at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:165)
                at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:152)
                at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:110)
                at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:53)
                at org.springframework.boot.logging.logback.LogbackLoggingSystem.configureByResourceUrl(LogbackLoggingSystem.java:199)
                at org.springframework.boot.logging.logback.LogbackLoggingSystem.loadConfiguration(LogbackLoggingSystem.java:165)
                ... 30 more
        Suppressed: java.io.FileNotFoundException: /home/<USER>/logs/poetize-error.log (没有那个文件或目录)
                at java.base/java.io.FileOutputStream.open0(Native Method)
                at java.base/java.io.FileOutputStream.open(FileOutputStream.java:293)
                at java.base/java.io.FileOutputStream.<init>(FileOutputStream.java:235)
                at ch.qos.logback.core.recovery.ResilientFileOutputStream.<init>(ResilientFileOutputStream.java:26)
                at ch.qos.logback.core.FileAppender.openFile(FileAppender.java:204)
                at ch.qos.logback.core.FileAppender.start(FileAppender.java:127)
                at ch.qos.logback.core.rolling.RollingFileAppender.start(RollingFileAppender.java:100)
                at ch.qos.logback.core.joran.action.AppenderAction.end(AppenderAction.java:90)
                at ch.qos.logback.core.joran.spi.Interpreter.callEndAction(Interpreter.java:309)
                at ch.qos.logback.core.joran.spi.Interpreter.endElement(Interpreter.java:193)
                at ch.qos.logback.core.joran.spi.Interpreter.endElement(Interpreter.java:179)
                at ch.qos.logback.core.joran.spi.EventPlayer.play(EventPlayer.java:62)
                at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:165)
                at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:152)
                at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:110)
                at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:53)
                at org.springframework.boot.logging.logback.LogbackLoggingSystem.configureByResourceUrl(LogbackLoggingSystem.java:199)
                at org.springframework.boot.logging.logback.LogbackLoggingSystem.loadConfiguration(LogbackLoggingSystem.java:165)
                ... 30 more
Exception in thread "main" java.lang.reflect.InvocationTargetException
        at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
        at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
        at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
        at java.base/java.lang.reflect.Method.invoke(Method.java:569)
        at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
        at org.springframework.boot.loader.Launcher.launch(Launcher.java:108)
        at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
        at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65)
Caused by: java.lang.IllegalStateException: java.lang.IllegalStateException: Logback configuration error detected: 
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[fileInfoLog] - Failed to create parent directories for [/home/<USER>/logs/poetize.log]
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[fileInfoLog] - openFile(/home/<USER>/logs/poetize.log,true) call failed. java.io.FileNotFoundException: /home/<USER>/logs/poetize.log (没有那个文件或目录)
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[fileErrorLog] - Failed to create parent directories for [/home/<USER>/logs/poetize-error.log]
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[fileErrorLog] - openFile(/home/<USER>/logs/poetize-error.log,true) call failed. java.io.FileNotFoundException: /home/<USER>/logs/poetize-error.log (没有那个文件或目录)
        at org.springframework.boot.context.logging.LoggingApplicationListener.initializeSystem(LoggingApplicationListener.java:344)
        at org.springframework.boot.context.logging.LoggingApplicationListener.initialize(LoggingApplicationListener.java:298)
        at org.springframework.boot.context.logging.LoggingApplicationListener.onApplicationEnvironmentPreparedEvent(LoggingApplicationListener.java:246)
        at org.springframework.boot.context.logging.LoggingApplicationListener.onApplicationEvent(LoggingApplicationListener.java:223)
        at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178)
        at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171)
        at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145)
        at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:133)
        at org.springframework.boot.context.event.EventPublishingRunListener.environmentPrepared(EventPublishingRunListener.java:85)
        at org.springframework.boot.SpringApplicationRunListeners.lambda$environmentPrepared$2(SpringApplicationRunListeners.java:66)
        at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
        at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120)
        at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:114)
        at org.springframework.boot.SpringApplicationRunListeners.environmentPrepared(SpringApplicationRunListeners.java:65)
        at org.springframework.boot.SpringApplication.prepareEnvironment(SpringApplication.java:344)
        at org.springframework.boot.SpringApplication.run(SpringApplication.java:302)
        at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
        at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
        at com.ld.poetry.PoetryApplication.main(PoetryApplication.java:14)
        ... 8 more
Caused by: java.lang.IllegalStateException: Logback configuration error detected: 
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[fileInfoLog] - Failed to create parent directories for [/home/<USER>/logs/poetize.log]
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[fileInfoLog] - openFile(/home/<USER>/logs/poetize.log,true) call failed. java.io.FileNotFoundException: /home/<USER>/logs/poetize.log (没有那个文件或目录)
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[fileErrorLog] - Failed to create parent directories for [/home/<USER>/logs/poetize-error.log]
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[fileErrorLog] - openFile(/home/<USER>/logs/poetize-error.log,true) call failed. java.io.FileNotFoundException: /home/<USER>/logs/poetize-error.log (没有那个文件或目录)
        at org.springframework.boot.logging.logback.LogbackLoggingSystem.reportConfigurationErrorsIfNecessary(LogbackLoggingSystem.java:189)
        at org.springframework.boot.logging.logback.LogbackLoggingSystem.loadConfiguration(LogbackLoggingSystem.java:170)
        at org.springframework.boot.logging.AbstractLoggingSystem.initializeWithConventions(AbstractLoggingSystem.java:80)
        at org.springframework.boot.logging.AbstractLoggingSystem.initialize(AbstractLoggingSystem.java:60)
        at org.springframework.boot.logging.logback.LogbackLoggingSystem.initialize(LogbackLoggingSystem.java:132)
2025-08-08 18:31:08.811 ERROR 417753 --- [           main] o.s.boot.SpringApplication               : Application run failed

java.lang.IllegalStateException: java.lang.IllegalStateException: Logback configuration error detected: 
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[fileInfoLog] - Failed to create parent directories for [/home/<USER>/logs/poetize.log]
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[fileInfoLog] - openFile(/home/<USER>/logs/poetize.log,true) call failed. java.io.FileNotFoundException: /home/<USER>/logs/poetize.log (没有那个文件或目录)
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[fileErrorLog] - Failed to create parent directories for [/home/<USER>/logs/poetize-error.log]
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[fileErrorLog] - openFile(/home/<USER>/logs/poetize-error.log,true) call failed. java.io.FileNotFoundException: /home/<USER>/logs/poetize-error.log (没有那个文件或目录)
        at org.springframework.boot.context.logging.LoggingApplicationListener.initializeSystem(LoggingApplicationListener.java:344)
        at org.springframework.boot.context.logging.LoggingApplicationListener.initialize(LoggingApplicationListener.java:298)
        at org.springframework.boot.context.logging.LoggingApplicationListener.onApplicationEnvironmentPreparedEvent(LoggingApplicationListener.java:246)
        at org.springframework.boot.context.logging.LoggingApplicationListener.onApplicationEvent(LoggingApplicationListener.java:223)
        at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:178)
        at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:171)
        at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:145)
        at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:133)
        at org.springframework.boot.context.event.EventPublishingRunListener.environmentPrepared(EventPublishingRunListener.java:85)
        at org.springframework.boot.SpringApplicationRunListeners.lambda$environmentPrepared$2(SpringApplicationRunListeners.java:66)
        at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
        at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:120)
        at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:114)
        at org.springframework.boot.SpringApplicationRunListeners.environmentPrepared(SpringApplicationRunListeners.java:65)
        at org.springframework.boot.SpringApplication.prepareEnvironment(SpringApplication.java:344)
        at org.springframework.boot.SpringApplication.run(SpringApplication.java:302)
        at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300)
        at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289)
        at com.ld.poetry.PoetryApplication.main(PoetryApplication.java:14)
        at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
        at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
        at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
        at java.base/java.lang.reflect.Method.invoke(Method.java:569)
        at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
        at org.springframework.boot.loader.Launcher.launch(Launcher.java:108)
        at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
        at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:65)
Caused by: java.lang.IllegalStateException: Logback configuration error detected: 
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[fileInfoLog] - Failed to create parent directories for [/home/<USER>/logs/poetize.log]
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[fileInfoLog] - openFile(/home/<USER>/logs/poetize.log,true) call failed. java.io.FileNotFoundException: /home/<USER>/logs/poetize.log (没有那个文件或目录)
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[fileErrorLog] - Failed to create parent directories for [/home/<USER>/logs/poetize-error.log]
ERROR in ch.qos.logback.core.rolling.RollingFileAppender[fileErrorLog] - openFile(/home/<USER>/logs/poetize-error.log,true) call failed. java.io.FileNotFoundException: /home/<USER>/logs/poetize-error.log (没有那个文件或目录)
        at org.springframework.boot.logging.logback.LogbackLoggingSystem.reportConfigurationErrorsIfNecessary(LogbackLoggingSystem.java:189)
        at org.springframework.boot.logging.logback.LogbackLoggingSystem.loadConfiguration(LogbackLoggingSystem.java:170)
        at org.springframework.boot.logging.AbstractLoggingSystem.initializeWithConventions(AbstractLoggingSystem.java:80)
        at org.springframework.boot.logging.AbstractLoggingSystem.initialize(AbstractLoggingSystem.java:60)
        at org.springframework.boot.logging.logback.LogbackLoggingSystem.initialize(LogbackLoggingSystem.java:132)
        at org.springframework.boot.context.logging.LoggingApplicationListener.initializeSystem(LoggingApplicationListener.java:329)
        ... 26 common frames omitted
        Suppressed: java.io.FileNotFoundException: /home/<USER>/logs/poetize.log (没有那个文件或目录)
                at java.base/java.io.FileOutputStream.open0(Native Method)
                at java.base/java.io.FileOutputStream.open(FileOutputStream.java:293)
                at java.base/java.io.FileOutputStream.<init>(FileOutputStream.java:235)
                at ch.qos.logback.core.recovery.ResilientFileOutputStream.<init>(ResilientFileOutputStream.java:26)
                at ch.qos.logback.core.FileAppender.openFile(FileAppender.java:204)
                at ch.qos.logback.core.FileAppender.start(FileAppender.java:127)
                at ch.qos.logback.core.rolling.RollingFileAppender.start(RollingFileAppender.java:100)
                at ch.qos.logback.core.joran.action.AppenderAction.end(AppenderAction.java:90)
                at ch.qos.logback.core.joran.spi.Interpreter.callEndAction(Interpreter.java:309)
                at ch.qos.logback.core.joran.spi.Interpreter.endElement(Interpreter.java:193)
                at ch.qos.logback.core.joran.spi.Interpreter.endElement(Interpreter.java:179)
                at ch.qos.logback.core.joran.spi.EventPlayer.play(EventPlayer.java:62)
                at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:165)
                at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:152)
                at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:110)
                at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:53)
                at org.springframework.boot.logging.logback.LogbackLoggingSystem.configureByResourceUrl(LogbackLoggingSystem.java:199)
                at org.springframework.boot.logging.logback.LogbackLoggingSystem.loadConfiguration(LogbackLoggingSystem.java:165)
                ... 30 common frames omitted
        Suppressed: java.io.FileNotFoundException: /home/<USER>/logs/poetize-error.log (没有那个文件或目录)
                at java.base/java.io.FileOutputStream.open0(Native Method)
                at java.base/java.io.FileOutputStream.open(FileOutputStream.java:293)
                at java.base/java.io.FileOutputStream.<init>(FileOutputStream.java:235)
                at ch.qos.logback.core.recovery.ResilientFileOutputStream.<init>(ResilientFileOutputStream.java:26)
                at ch.qos.logback.core.FileAppender.openFile(FileAppender.java:204)
                at ch.qos.logback.core.FileAppender.start(FileAppender.java:127)
                at ch.qos.logback.core.rolling.RollingFileAppender.start(RollingFileAppender.java:100)
                at ch.qos.logback.core.joran.action.AppenderAction.end(AppenderAction.java:90)
                at ch.qos.logback.core.joran.spi.Interpreter.callEndAction(Interpreter.java:309)
                at ch.qos.logback.core.joran.spi.Interpreter.endElement(Interpreter.java:193)
                at ch.qos.logback.core.joran.spi.Interpreter.endElement(Interpreter.java:179)
                at ch.qos.logback.core.joran.spi.EventPlayer.play(EventPlayer.java:62)
                at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:165)
                at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:152)
                at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:110)
                at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:53)
                at org.springframework.boot.logging.logback.LogbackLoggingSystem.configureByResourceUrl(LogbackLoggingSystem.java:199)
                at org.springframework.boot.logging.logback.LogbackLoggingSystem.loadConfiguration(LogbackLoggingSystem.java:165)
                ... 30 common frames omitted

        at org.springframework.boot.context.logging.LoggingApplicationListener.initializeSystem(LoggingApplicationListener.java:329)
        ... 26 more
        Suppressed: java.io.FileNotFoundException: /home/<USER>/logs/poetize.log (没有那个文件或目录)
                at java.base/java.io.FileOutputStream.open0(Native Method)
                at java.base/java.io.FileOutputStream.open(FileOutputStream.java:293)
                at java.base/java.io.FileOutputStream.<init>(FileOutputStream.java:235)
                at ch.qos.logback.core.recovery.ResilientFileOutputStream.<init>(ResilientFileOutputStream.java:26)
                at ch.qos.logback.core.FileAppender.openFile(FileAppender.java:204)
                at ch.qos.logback.core.FileAppender.start(FileAppender.java:127)
                at ch.qos.logback.core.rolling.RollingFileAppender.start(RollingFileAppender.java:100)
                at ch.qos.logback.core.joran.action.AppenderAction.end(AppenderAction.java:90)
                at ch.qos.logback.core.joran.spi.Interpreter.callEndAction(Interpreter.java:309)
                at ch.qos.logback.core.joran.spi.Interpreter.endElement(Interpreter.java:193)
                at ch.qos.logback.core.joran.spi.Interpreter.endElement(Interpreter.java:179)
                at ch.qos.logback.core.joran.spi.EventPlayer.play(EventPlayer.java:62)
                at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:165)
                at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:152)
                at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:110)
                at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:53)
                at org.springframework.boot.logging.logback.LogbackLoggingSystem.configureByResourceUrl(LogbackLoggingSystem.java:199)
                at org.springframework.boot.logging.logback.LogbackLoggingSystem.loadConfiguration(LogbackLoggingSystem.java:165)
                ... 30 more
        Suppressed: java.io.FileNotFoundException: /home/<USER>/logs/poetize-error.log (没有那个文件或目录)
                at java.base/java.io.FileOutputStream.open0(Native Method)
                at java.base/java.io.FileOutputStream.open(FileOutputStream.java:293)
                at java.base/java.io.FileOutputStream.<init>(FileOutputStream.java:235)
                at ch.qos.logback.core.recovery.ResilientFileOutputStream.<init>(ResilientFileOutputStream.java:26)
                at ch.qos.logback.core.FileAppender.openFile(FileAppender.java:204)
                at ch.qos.logback.core.FileAppender.start(FileAppender.java:127)
                at ch.qos.logback.core.rolling.RollingFileAppender.start(RollingFileAppender.java:100)
                at ch.qos.logback.core.joran.action.AppenderAction.end(AppenderAction.java:90)
                at ch.qos.logback.core.joran.spi.Interpreter.callEndAction(Interpreter.java:309)
                at ch.qos.logback.core.joran.spi.Interpreter.endElement(Interpreter.java:193)
                at ch.qos.logback.core.joran.spi.Interpreter.endElement(Interpreter.java:179)
                at ch.qos.logback.core.joran.spi.EventPlayer.play(EventPlayer.java:62)
                at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:165)
                at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:152)
                at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:110)
                at ch.qos.logback.core.joran.GenericConfigurator.doConfigure(GenericConfigurator.java:53)
                at org.springframework.boot.logging.logback.LogbackLoggingSystem.configureByResourceUrl(LogbackLoggingSystem.java:199)
                at org.springframework.boot.logging.logback.LogbackLoggingSystem.loadConfiguration(LogbackLoggingSystem.java:165)
                ... 30 more
phy@phy-HP-431-Notebook-PC:/var/www/poetize-master/poetize-server/poetry-web$ 
