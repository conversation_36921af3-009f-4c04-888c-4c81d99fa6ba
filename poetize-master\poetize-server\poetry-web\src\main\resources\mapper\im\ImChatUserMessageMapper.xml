<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ld.poetry.im.http.dao.ImChatUserMessageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ld.poetry.im.http.entity.ImChatUserMessage">
        <id column="id" property="id" />
        <result column="from_id" property="fromId" />
        <result column="to_id" property="toId" />
        <result column="content" property="content" />
        <result column="message_status" property="messageStatus" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, from_id, to_id, content, message_status, create_time
    </sql>

</mapper>
