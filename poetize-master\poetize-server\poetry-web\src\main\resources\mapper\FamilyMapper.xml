<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ld.poetry.dao.FamilyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ld.poetry.entity.Family">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="bg_cover" property="bgCover"/>
        <result column="man_cover" property="manCover"/>
        <result column="woman_cover" property="womanCover"/>
        <result column="man_name" property="manName"/>
        <result column="woman_name" property="womanName"/>
        <result column="timing" property="timing"/>
        <result column="status" property="status"/>
        <result column="countdown_title" property="countdownTitle"/>
        <result column="countdown_time" property="countdownTime"/>
        <result column="family_info" property="familyInfo"/>
        <result column="like_count" property="likeCount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, bg_cover, man_cover, woman_cover, status, like_count, man_name, woman_name, timing, countdown_title, countdown_time, family_info, create_time, update_time
    </sql>

</mapper>
