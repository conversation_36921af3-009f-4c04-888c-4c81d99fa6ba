{"name": "poetry-im-ui", "version": "0.1.0", "private": true, "scripts": {"serve": "set NODE_OPTIONS=--openssl-legacy-provider & vue-cli-service serve", "build": "set NODE_OPTIONS=--openssl-legacy-provider & vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.27.2", "core-js": "^3.6.5", "crypto-js": "^4.1.1", "element-plus": "^2.2.10", "qs": "^6.10.3", "reconnecting-websocket": "^4.4.0", "vue": "^3.0.0", "vue-router": "^4.0.0-0", "vuex": "^4.0.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.13", "@vue/cli-plugin-eslint": "~4.5.13", "@vue/cli-plugin-router": "~4.5.13", "@vue/cli-service": "~4.5.13", "@vue/compiler-sfc": "^3.0.0", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "compression-webpack-plugin": "^4.0.0", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^7.0.0", "naive-ui": "^2.28.4", "vfonts": "^0.0.3"}}