# POETIZE 项目启动指南

## 📁 项目结构分析

您当前拥有的是 **POETIZE** 开源博客系统，这是一个功能完整的个人博客项目，完全符合您的需求！

```
poetize-master/
├── poetize-ui/              # Vue2 博客前端（主要前端）
├── poetize-im-ui/           # Vue3 聊天室前端（可选）
├── poetize-server/          # Spring Boot 后端
│   └── poetry-web/          # 主要后端项目
│   └── sql/                 # 数据库脚本
│       └── poetry.sql       # 数据库初始化文件
└── poetize_picture/         # 项目截图
```

## 🛠️ 技术栈

| 模块   | 技术栈            | 说明                    |
|------|----------------|----------------------|
| 后端   | SpringBoot 2.7 | Java + MySQL + MyBatis-Plus |
| 博客   | Vue2           | Element UI + 完美响应式设计    |
| 聊天室  | Vue3           | Element-Plus + Naive UI   |

## 🚀 启动步骤

### 第一步：环境准备

确保您的服务器已安装：
- ✅ Java 8+ (推荐 Java 11+)
- ✅ MySQL 5.7+ (推荐 MySQL 8.0+)
- ✅ Node.js 14+ (推荐 Node.js 16+)
- ✅ Maven 3.6+

### 第二步：数据库配置

1. **创建数据库**：
```sql
CREATE DATABASE poetry CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. **导入数据库脚本**：
```bash
mysql -u root -p poetry < poetize-master/poetize-server/sql/poetry.sql
```

### 第三步：后端启动

1. **进入后端目录**：
```bash
cd poetize-master/poetize-server/poetry-web
```

2. **配置数据库连接**：
编辑 `src/main/resources/application.yml`，修改数据库连接信息：
```yaml
spring:
  datasource:
    url: ********************************************************************************************************************
    username: root
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver
```

3. **启动后端**：
```bash
mvn spring-boot:run
```
或者使用IDE打开项目，运行 `PoetryApplication` 主类

**后端启动成功标志**：控制台显示 "Started PoetryApplication"，默认端口 8080

### 第四步：前端启动（博客主站）

1. **进入博客前端目录**：
```bash
cd poetize-master/poetize-ui
```

2. **安装依赖**：
```bash
npm install
```

3. **启动开发服务器**：
```bash
npm run serve
```

**前端启动成功标志**：浏览器自动打开 http://localhost:8081

### 第五步：聊天室前端启动（可选）

1. **进入聊天室前端目录**：
```bash
cd poetize-master/poetize-im-ui
```

2. **安装依赖**：
```bash
npm install
```

3. **启动开发服务器**：
```bash
npm run serve
```

**聊天室启动成功标志**：运行在 http://localhost:8082

## 🌐 访问地址

启动成功后，您可以访问：

- **博客主站**: http://localhost:8081
- **后台管理**: http://localhost:8081/admin
- **聊天室**: http://localhost:8082 (可选)
- **后端API**: http://localhost:8080

## 🔧 生产环境部署

### 1. 前端构建

**博客前端**：
```bash
cd poetize-master/poetize-ui
npm run build
# 构建文件在 dist/ 目录
```

**聊天室前端**（可选）：
```bash
cd poetize-master/poetize-im-ui
npm run build
# 构建文件在 dist/ 目录
```

### 2. 后端打包

```bash
cd poetize-master/poetize-server/poetry-web
mvn clean package
# 生成 target/poetry-web-2.0.jar
```

### 3. Nginx 配置示例

```nginx
server {
    listen 443 ssl http2;
    server_name penghaiyi.dpdns.org;
    
    # SSL配置...
    
    # 博客前端
    location / {
        root /var/www/poetize/poetize-ui/dist;
        try_files $uri $uri/ /index.html;
        index index.html;
    }
    
    # 聊天室前端（可选）
    location /im {
        alias /var/www/poetize/poetize-im-ui/dist;
        try_files $uri $uri/ /im/index.html;
    }
    
    # 后端API代理
    location /api/ {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 文件上传代理
    location /upload/ {
        proxy_pass http://localhost:8080;
        client_max_body_size 10M;
    }
}
```

### 4. 后端服务配置

创建 systemd 服务文件 `/etc/systemd/system/poetize.service`：

```ini
[Unit]
Description=Poetize Blog Application
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/var/www/poetize
ExecStart=/usr/bin/java -jar /var/www/poetize/poetry-web-2.0.jar
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable poetize
sudo systemctl start poetize
```

## ⚙️ 重要配置

### 1. 后台管理配置

- 访问：http://your-domain/admin
- 默认账号需要在数据库中查看或创建
- 在后台可以配置网站基本信息、邮箱设置等

### 2. 文件存储配置

- 默认使用本地存储
- 可配置七牛云等云存储
- 文件通过 Nginx 代理访问

### 3. 邮件配置

在后台管理或配置文件中设置邮箱信息，用于：
- 用户注册验证
- 留言通知
- 找回密码

## 🎯 功能特色

这个项目完美符合您的需求：

✅ **个人博客主页** - 美观的博客界面
✅ **功能网站导航** - 可以添加链接跳转到其他网站
✅ **响应式设计** - 完美适配手机、平板、桌面
✅ **后台管理** - 完整的内容管理系统
✅ **多种功能** - 文章、相册、音乐、留言、友链等

## 🔍 常见问题

### Q: 启动时出现端口冲突？
A: 修改配置文件中的端口号，或停止占用端口的服务

### Q: 数据库连接失败？
A: 检查数据库服务是否启动，用户名密码是否正确

### Q: 前端页面空白？
A: 检查后端是否启动，API接口是否可访问

### Q: 文件上传失败？
A: 检查文件存储路径权限，确保应用有写入权限

## 📞 技术支持

- 项目地址：https://gitee.com/littledokey/poetize
- 问题反馈：查看项目 Issues
- QQ交流群：807489974

这个项目就是您需要的个人博客系统，可以直接使用！
