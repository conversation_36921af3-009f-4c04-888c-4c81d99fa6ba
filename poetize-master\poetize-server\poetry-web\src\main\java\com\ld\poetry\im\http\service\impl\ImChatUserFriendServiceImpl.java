package com.ld.poetry.im.http.service.impl;

import com.ld.poetry.im.http.entity.ImChatUserFriend;
import com.ld.poetry.im.http.dao.ImChatUserFriendMapper;
import com.ld.poetry.im.http.service.ImChatUserFriendService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 好友 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-02
 */
@Service
public class ImChatUserFriendServiceImpl extends ServiceImpl<ImChatUserFriendMapper, ImChatUserFriend> implements ImChatUserFriendService {

}
