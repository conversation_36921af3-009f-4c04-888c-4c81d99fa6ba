<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ld.poetry.dao.HistoryInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ld.poetry.entity.HistoryInfo">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="ip" property="ip"/>
        <result column="nation" property="nation"/>
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, ip, nation, province, city, create_time
    </sql>

</mapper>
